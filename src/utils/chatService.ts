import { HumanMessage, AIMessage } from '@langchain/core/messages';
import { createChatModel, loadSelectedModel, type SupportedModel } from './langchainConfig';
import { getMemoryLimit } from './memoryUtils';

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: number;
  isLoading?: boolean; // Optional flag to indicate loading state
}

export interface Conversation {
  id: string;
  messages: ChatMessage[];
  title?: string;
  createdAt?: number;
  lastModified?: number;
}

/**
 * Removes all content between <think> tags, including the tags themselves
 */
export function removeThinkTags(text: string): string {
  return text.replace(/<think>[\s\S]*?<\/think>/g, '').trim();
}

// Streaming callback type for progressive text updates
export type StreamingCallback = (chunk: string) => void;

// Streaming controller for cancellation support
export class StreamingController {
  private abortController: AbortController;
  private isCancelled: boolean = false;

  constructor() {
    this.abortController = new AbortController();
  }

  get signal(): AbortSignal {
    return this.abortController.signal;
  }

  get cancelled(): boolean {
    return this.isCancelled;
  }

  cancel(): void {
    this.isCancelled = true;
    this.abortController.abort();
  }

  reset(): void {
    this.isCancelled = false;
    this.abortController = new AbortController();
  }
}

export async function processMessage(
  conversation: Conversation,
  message: string,
  selectedModelId?: string,
  onStreamingChunk?: StreamingCallback,
  streamingController?: StreamingController
): Promise<string> {
  console.log('processMessage called with:', {
    conversationId: conversation.id,
    messageLength: message.length,
    selectedModelId,
    isStreaming: !!onStreamingChunk,
    messagePreview: message.substring(0, 100) + (message.length > 100 ? '...' : '')
  });

  // Filter out content between <think> tags
  const cleanedMessage = removeThinkTags(message);
  console.log('Message after cleaning think tags:', {
    originalLength: message.length,
    cleanedLength: cleanedMessage.length,
    cleanedPreview: cleanedMessage.substring(0, 100) + (cleanedMessage.length > 100 ? '...' : '')
  });

  // Get user's selected message memory limit using utility function
  const memoryLimit = getMemoryLimit();
  const recentMessages = conversation.messages.slice(-memoryLimit);

  console.log(`Using memory limit: ${memoryLimit}, processing ${recentMessages.length} of ${conversation.messages.length} total messages`);

  // Convert messages to LangChain format
  const messagesToInvoke = recentMessages.map(msg => {
    const content = { content: msg.content };
    return msg.role === 'user'
      ? new HumanMessage(content)
      : new AIMessage(content);
  });

  // Add the current user message
  const currentUserMessage = new HumanMessage({ content: cleanedMessage });
  messagesToInvoke.push(currentUserMessage);

  // Log the messages being sent to the model for debugging
  console.log('Messages to be sent to model:', messagesToInvoke.map((msg, index) => ({
    index,
    type: msg.constructor.name,
    contentPreview: typeof msg.content === 'object' && msg.content.content
      ? msg.content.content.substring(0, 100) + (msg.content.content.length > 100 ? '...' : '')
      : String(msg.content).substring(0, 100)
  })));

  try {
    console.log('Creating chat model for processing message');
    let selectedModel: SupportedModel | null = null;

    // Determine which model to use - prioritize explicit model ID, then check localStorage for initial message
    if (selectedModelId) {
      // Use the explicitly provided model ID if available
      const availableModels = await import('./langchainConfig').then(m => m.AVAILABLE_MODELS);
      selectedModel = availableModels.find(m => m.id === selectedModelId) || loadSelectedModel();
      console.log('Using explicitly provided model ID:', selectedModelId);
    } else {
      // Check for initial message data for model selection only
      const initialMessageData = localStorage.getItem('rohit_initialMessage');
      console.log('Initial message data in localStorage:', !!initialMessageData);

      if (initialMessageData) {
        try {
          const { model } = JSON.parse(initialMessageData);
          selectedModel = model;
          console.log('Using model from initial message data:', model?.name);
          // Clear the initial message data as it's been used for model selection
          localStorage.removeItem('rohit_initialMessage');
        } catch (error) {
          console.error('Error parsing initial message data:', error);
          selectedModel = loadSelectedModel();
        }
      } else {
        // For subsequent messages, use the currently selected model
        selectedModel = loadSelectedModel();
        console.log('Using currently selected model');
      }
    }

    if (!selectedModel) {
      console.error('No model selected for message processing');
      throw new Error('No model selected. Please select a model in Settings.');
    }

    console.log('Selected model for processing:', selectedModel.name, '(', selectedModel.provider, ')');

    // Check if we need to adapt content for different providers
    if (selectedModel.provider !== 'gemini' && messagesToInvoke.length > 0) {
      // For non-Gemini models, we might need to truncate large content
      const lastMessage = messagesToInvoke[messagesToInvoke.length - 1];
      if (typeof lastMessage.content === 'object' && typeof lastMessage.content.content === 'string') {
        const content = lastMessage.content.content;

        // If content is very large and not Gemini, we should truncate
        if (content.length > 50000) {
          console.log(`Large message detected (${content.length} chars) for ${selectedModel.provider} model. Truncating.`);
          const MAX_TOKENS = 4000;
          const estimatedCurrentTokens = Math.ceil(content.length / 4);

          if (estimatedCurrentTokens > MAX_TOKENS) {
            // Truncate to approximately 4000 tokens
            const keepRatio = MAX_TOKENS / estimatedCurrentTokens;
            const charsToKeep = Math.floor(content.length * keepRatio);

            // Create a truncated version with warning
            const truncatedContent = content.substring(0, charsToKeep) +
              "\n\n[Content truncated due to model token limitations. Please use Gemini models for larger content or select fewer files.]";

            // Replace the content
            lastMessage.content.content = truncatedContent;
            console.log(`Truncated message to approximately ${Math.ceil(truncatedContent.length / 4)} tokens`);
          }
        }
      }
    }

    console.log('Creating chat model with ID:', selectedModel.id);
    const model = await createChatModel(selectedModel.id);
    console.log('Invoking model with', messagesToInvoke.length, 'messages');

    // Check if streaming is requested and if the provider supports it
    const supportsStreaming = selectedModel.provider === 'ollama' || selectedModel.provider === 'openai' ||
                             selectedModel.provider === 'anthropic' || selectedModel.provider === 'groq';

    if (onStreamingChunk && supportsStreaming) {
      console.log('Using streaming mode for provider:', selectedModel.provider);
      console.time('modelStreamInvocation');

      let fullResponse = '';

      try {
        // Use the stream method for real-time streaming
        const stream = await (model as any).stream(messagesToInvoke);

        for await (const chunk of stream) {
          // Check if streaming was cancelled
          if (streamingController?.cancelled) {
            console.log('Streaming cancelled by user');
            break;
          }

          const chunkContent = chunk.content || '';
          if (chunkContent) {
            fullResponse += chunkContent;
            // Call the streaming callback with the new chunk
            onStreamingChunk(chunkContent);
          }
        }

        console.timeEnd('modelStreamInvocation');

        if (streamingController?.cancelled) {
          console.log('Streaming was cancelled, returning partial response, length:', fullResponse.length);
          const cleanedResponse = removeThinkTags(fullResponse);
          return cleanedResponse + '\n\n[Response stopped by user]';
        }

        console.log('Streaming completed, total response length:', fullResponse.length);

        // Filter out any <think> tags from the final response
        const cleanedResponse = removeThinkTags(fullResponse);
        console.log('Returning cleaned streaming response, length:', cleanedResponse.length);
        return cleanedResponse;

      } catch (streamError) {
        // Check if the error is due to cancellation
        if (streamError instanceof Error && streamError.name === 'AbortError') {
          console.log('Streaming aborted by user');
          const cleanedResponse = removeThinkTags(fullResponse);
          return cleanedResponse + '\n\n[Response stopped by user]';
        }

        console.error('Streaming failed, falling back to invoke:', streamError);
        // Fall back to regular invoke if streaming fails
      }
    }

    // Use regular invoke method (fallback or for non-streaming providers)
    console.log('Using regular invoke mode');
    console.time('modelInvocation');
    const response = await (model as any).invoke(messagesToInvoke);
    console.timeEnd('modelInvocation');
    console.log('Received response from model');

    console.log('Response type:', typeof response.content, Array.isArray(response.content) ? 'array' : '');

    if (typeof response.content === 'string') {
      // Filter out any <think> tags from the response
      const cleanedResponse = removeThinkTags(response.content);
      console.log('Returning cleaned string response, length:', cleanedResponse.length);
      return cleanedResponse;
    } else if (Array.isArray(response.content)) {
      // If content is an array of message parts, join them after filtering <think> tags
      return response.content.map((part: unknown) => {
        if (typeof part === 'string') {
          return removeThinkTags(part);
        }
        return JSON.stringify(part);
      }).join('').trim();
    }

    return removeThinkTags(JSON.stringify(response.content));
  } catch (error) {
    console.error('Error processing message:', error);
    console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace available');
    if (error instanceof Error) {
      throw new Error(error.message);
    }
    throw new Error('Failed to process message. Please try again.');
  }
}