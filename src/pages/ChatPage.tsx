import React, { useState, useRef, useEffect, useCallback } from 'react';
import { type SupportedModel, saveSelectedModel, saveSelectedProvider, loadSelectedModel } from '../utils/langchainConfig';
import { processMessage, type ChatMessage, type Conversation, type StreamingCallback, StreamingController } from '../utils/chatService';
import * as conversationService from '../services/conversationService';
import { getMemoryLimit, calculateContextSize, formatMemoryContext } from '../utils/memoryUtils';
import styles from './ChatPage.module.css';
import loadingStyles from '../styles/LoadingIndicator.module.css';
import LeftPanel from '../components/LeftPanel';
import RightPanel from '../components/RightPanel';
import ChatTopMenuBar from '../components/ChatTopMenuBar';
import LoadingAnimation from '../components/LoadingAnimation';
import StreamingMessage from '../components/StreamingMessage';

const ChatPage: React.FC = () => {
  const [isDbInitializing, setIsDbInitializing] = useState(true);
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [activeConversationId, setActiveConversationId] = useState<string | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedModel, setSelectedModel] = useState<SupportedModel | null>(loadSelectedModel());
  const [showLeftPanel, setShowLeftPanel] = useState(true);
  const [showRightPanel, setShowRightPanel] = useState(false);
  const [rightPanelWidth, setRightPanelWidth] = useState(300);
  const [extractedSvg, setExtractedSvg] = useState<string>('');
  const [memoryLimit, setMemoryLimit] = useState(getMemoryLimit());
  const [streamingMessageId, setStreamingMessageId] = useState<string | null>(null);
  const [streamingContent, setStreamingContent] = useState<string>('');
  const [streamingController, setStreamingController] = useState<StreamingController | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const lastMessageTime = useRef<number>(0);
  const MIN_MESSAGE_INTERVAL = 2000; // Minimum 2 seconds between messages

  const loadData = async () => {
    try {
      console.log("Initializing database and loading conversations...");

      // Clean up any potentially corrupted localStorage data
      const cleanupStaleData = () => {
        const keysToCheck = ['rohit_last_processed_timestamp'];
        keysToCheck.forEach(key => {
          const value = localStorage.getItem(key);
          if (value) {
            const timestamp = parseInt(value, 10);
            const now = Date.now();
            // Remove timestamps older than 1 hour to prevent stale data issues
            if (now - timestamp > 60 * 60 * 1000) {
              console.log(`Removing stale localStorage key: ${key}`);
              localStorage.removeItem(key);
            }
          }
        });
      };

      cleanupStaleData();

      // Initialize the database first
      await conversationService.initializeService();

      // Add a small delay to ensure IndexedDB is fully initialized
      await new Promise(resolve => setTimeout(resolve, 200));

      // Load conversations
      console.log("Loading conversations from database...");
      const loadedConversations = await conversationService.loadConversations();
      console.log(`Loaded ${loadedConversations.length} conversations`);

      // Update state with loaded conversations
      setConversations(loadedConversations);

      if (loadedConversations.length > 0) {
        const mostRecentConversation = loadedConversations[0];
        console.log("Setting active conversation to most recent:", mostRecentConversation.id);

        // Get the full conversation with all messages
        console.log("Fetching full conversation details...");
        const fullConversation = await conversationService.getConversation(mostRecentConversation.id);
        if (fullConversation) {
          console.log(`Loaded full conversation with ${fullConversation.messages.length} messages`);
          setActiveConversationId(fullConversation.id);
          setMessages(fullConversation.messages);
        } else {
          // Fallback to the conversation from the list if we can't get the full one
          console.log(`Using conversation from list with ${mostRecentConversation.messages.length} messages`);
          setActiveConversationId(mostRecentConversation.id);
          setMessages(mostRecentConversation.messages);
        }
      } else {
        console.log("No conversations found, starting with empty state");
        setActiveConversationId(null);
        setMessages([]);
      }

      // Mark database as initialized
      setIsDbInitializing(false);
      console.log("Database initialization complete");
    } catch (error) {
      console.error('Error loading data:', error);
      setIsDbInitializing(false);
    }
  };

  // Function to process initial message from localStorage
  const processInitialMessage = useCallback(async () => {
    if (isDbInitializing) {
      // Wait for DB to be ready before processing initial message
      return;
    }

    const initialMessageData = localStorage.getItem('rohit_initialMessage');

    if (initialMessageData) {
      try {
        const { model, initialMessage, timestamp } = JSON.parse(initialMessageData);

        console.log('Processing initial message:', {
          messageLength: initialMessage?.length,
          messagePreview: initialMessage?.substring(0, 100) + (initialMessage?.length > 100 ? '...' : ''),
          modelName: model?.name,
          timestamp,
          fullMessage: initialMessage // Log the full message to debug corruption
        });

        // Validate that the initial message is what we expect
        if (!initialMessage || typeof initialMessage !== 'string') {
          console.error('Invalid initial message data:', initialMessage);
          localStorage.removeItem('rohit_initialMessage');
          return;
        }

        // Check if we've already processed this message
        const lastProcessedTimestamp = localStorage.getItem('rohit_last_processed_timestamp');
        if (lastProcessedTimestamp && lastProcessedTimestamp === timestamp.toString()) {
          console.log('This message has already been processed, skipping');
          localStorage.removeItem('rohit_initialMessage');
          return;
        }

        // Set the selected model if available
        if (model) {
          setSelectedModel(model);
        }

        // Set the initial message
        if (initialMessage) {
          setInputValue(initialMessage);

          // Clear any existing conversation state to ensure clean start
          setActiveConversationId(null);
          setMessages([]);

          // Create the user message
          const userMessage: ChatMessage = {
            id: `msg_${Date.now()}_user_${Math.random().toString(36).substring(2, 9)}`,
            content: initialMessage,
            role: 'user',
            timestamp: Date.now(),
          };

          console.log('Created user message for initial processing:', {
            id: userMessage.id,
            content: userMessage.content,
            contentLength: userMessage.content.length
          });

          // Set messages to show the user message immediately
          setMessages([userMessage]);

          try {
            setIsProcessing(true);

            // Create a completely new conversation
            const title = initialMessage.length > 30
              ? initialMessage.substring(0, 27) + '...'
              : initialMessage;
            const newConversation = await conversationService.createConversation(title);
            if (!newConversation) throw new Error('Failed to create conversation');

            console.log('Created new conversation:', newConversation.id);

            // Add the user message to the conversation
            const updatedConversation = await conversationService.addMessageToConversation(newConversation.id, userMessage);
            if (!updatedConversation) throw new Error('Failed to add message to conversation');

            setActiveConversationId(newConversation.id);

            // Mark this message as processed BEFORE calling processMessage
            localStorage.setItem('rohit_last_processed_timestamp', timestamp.toString());

            // Create a placeholder AI message for streaming
            const aiMessageId = `msg_${Date.now()}_asst_${Math.random().toString(36).substring(2, 9)}`;
            const assistantMessage: ChatMessage = {
              id: aiMessageId,
              content: '',
              role: 'assistant',
              timestamp: Date.now(),
            };

            // Add both messages to UI immediately
            setMessages([userMessage, assistantMessage]);
            setStreamingMessageId(aiMessageId);
            setStreamingContent('');

            // Create and set streaming controller
            const controller = new StreamingController();
            setStreamingController(controller);

            // Define streaming callback
            const onStreamingChunk: StreamingCallback = (chunk: string) => {
              setStreamingContent(prev => prev + chunk);
            };

            // Process with AI using streaming - use empty conversation to ensure clean processing
            const tempConversation: Conversation = {
              id: 'temp',
              messages: [], // Empty messages array to ensure only current message is processed
              title: initialMessage.substring(0, 30),
              createdAt: Date.now(),
              lastModified: Date.now()
            };

            console.log('About to call processMessage for initial message:', {
              conversationId: tempConversation.id,
              messageToProcess: initialMessage,
              messageLength: initialMessage.length,
              messagePreview: initialMessage.substring(0, 100),
              modelId: model?.id,
              modelName: model?.name
            });

            const response = await processMessage(tempConversation, initialMessage, model?.id, onStreamingChunk, controller);

            // Update the assistant message with final response
            const finalAssistantMessage: ChatMessage = {
              ...assistantMessage,
              content: response,
            };

            setMessages([userMessage, finalAssistantMessage]);

            // Add the final assistant message to the conversation
            await conversationService.addMessageToConversation(newConversation.id, finalAssistantMessage);

            // Refresh conversation list
            const updatedConversations = await conversationService.loadConversations();
            setConversations(updatedConversations);
          } catch (error) {
            console.error('Error processing initial message:', error);
          } finally {
            setIsProcessing(false);
            setStreamingMessageId(null);
            setStreamingContent('');
            setStreamingController(null);
            // Clear the localStorage item AFTER processing is complete
            localStorage.removeItem('rohit_initialMessage');
          }
        }
      } catch (error) {
        console.error('Error parsing initial message data:', error);
        localStorage.removeItem('rohit_initialMessage');
      }
    }
  }, [isDbInitializing, setSelectedModel]);

  useEffect(() => {
    // Load existing conversations
    loadData();
  }, []);

  useEffect(() => {
    // Process any initial message from HomePage after DB is initialized
    if (!isDbInitializing) {
      processInitialMessage();
    }
  }, [isDbInitializing, processInitialMessage]);

  // Listen for memory limit changes
  useEffect(() => {
    const handleStorageChange = () => {
      const newMemoryLimit = getMemoryLimit();
      setMemoryLimit(newMemoryLimit);
    };

    // Listen for storage changes
    window.addEventListener('storage', handleStorageChange);

    // Also check periodically in case changes happen in the same tab
    const interval = setInterval(handleStorageChange, 1000);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      clearInterval(interval);
    };
  }, []);

  const scrollToBottom = (behavior: 'auto' | 'smooth' = 'smooth') => {
    messagesEndRef.current?.scrollIntoView({ behavior });
  };

  // Extract SVG from messages
  useEffect(() => {
    const extractSvgFromMessages = () => {
      // Check the most recent assistant message
      const assistantMessages = messages.filter(msg => msg.role === 'assistant');
      if (assistantMessages.length === 0) return;

      const lastAssistantMessage = assistantMessages[assistantMessages.length - 1];
      const content = lastAssistantMessage.content;

      // Look for SVG content patterns - find all SVGs in the message
      const svgRegex = /<svg[\s\S]*?<\/svg>/g;
      const matches = Array.from(content.matchAll(svgRegex));

      // Get the last SVG match if multiple are found
      if (matches && matches.length > 0) {
        const lastSvg = matches[matches.length - 1][0];
        setExtractedSvg(lastSvg);
        // Automatically open the right panel when SVG is found
        setShowRightPanel(true);
      }
    };

    extractSvgFromMessages();
    scrollToBottom();
  }, [messages]);

  // Scroll to bottom when starting to process a message
  useEffect(() => {
    if (isProcessing) {
      scrollToBottom('auto');
    }
  }, [isProcessing]);

  // Scroll to bottom when streaming content updates
  useEffect(() => {
    if (streamingContent) {
      scrollToBottom('auto');
    }
  }, [streamingContent]);

  const handleSelectConversation = useCallback(async (conversationId: string) => {
    console.log(`handleSelectConversation called with ID: ${conversationId}`);

    try {
      // Always fetch from the database to ensure we have the latest data
      console.log("Fetching conversation from database:", conversationId);
      const fetchedConv = await conversationService.getConversation(conversationId);

      if (fetchedConv) {
        console.log("Setting active conversation:", fetchedConv.id, "with", fetchedConv.messages.length, "messages");

        // Update UI state
        setActiveConversationId(fetchedConv.id);
        setMessages(fetchedConv.messages);

        // Add a small delay to ensure any pending IndexedDB operations complete
        await new Promise(resolve => setTimeout(resolve, 100));

        // Refresh conversations list to ensure UI is up to date
        console.log("Refreshing conversations list in handleSelectConversation");
        const updatedConversations = await conversationService.loadConversations();
        console.log(`Refreshed conversations list, found ${updatedConversations.length} conversations`);
        setConversations(updatedConversations);
      } else {
        console.error("Could not find conversation with ID:", conversationId);

        // Fallback to the conversation from the current state if available
        const stateConv = conversations.find(c => c.id === conversationId);
        if (stateConv) {
          console.log("Using conversation from state as fallback");
          setActiveConversationId(stateConv.id);
          setMessages(stateConv.messages);
        }
      }
    } catch (error) {
      console.error("Error in handleSelectConversation:", error);
    }
  }, [conversations]);

  const handleNewChat = useCallback(async () => {
    console.log("Starting new chat");
    setActiveConversationId(null);
    setMessages([]);
    setInputValue('');

    // Add a small delay to ensure any pending IndexedDB operations complete
    await new Promise(resolve => setTimeout(resolve, 100));

    // Force refresh the conversations list to ensure UI is up to date
    console.log("Refreshing conversations list in handleNewChat");
    try {
      const updatedConversations = await conversationService.loadConversations();
      console.log(`Refreshed conversations list, found ${updatedConversations.length} conversations`);
      setConversations(updatedConversations);
    } catch (error) {
      console.error("Error refreshing conversations list:", error);
    }
  }, []);

  const handleModelSelect = useCallback((model: SupportedModel) => {
    setSelectedModel(model);
    saveSelectedModel(model.id);
    saveSelectedProvider(model.provider);
  }, []);

  const handleStopGeneration = useCallback(() => {
    console.log('Stop generation requested by user');
    if (streamingController) {
      streamingController.cancel();
      console.log('Streaming controller cancelled');
    }
    // The cleanup will be handled in the finally block of handleSendMessage
  }, [streamingController]);

  const handleSendMessage = async (messageContent: string) => {
    if (!messageContent.trim() || isProcessing) return;

    const now = Date.now();
    if (now - lastMessageTime.current < MIN_MESSAGE_INTERVAL) {
      console.log('Message sent too quickly, ignoring');
      return;
    }
    lastMessageTime.current = now;

    setIsProcessing(true);

    try {
      let currentConversationId = activeConversationId;

      // Create a new conversation if none exists
      if (!currentConversationId) {
        const newConversation = await conversationService.createConversation('New Chat');
        if (!newConversation) throw new Error('Failed to create conversation');
        currentConversationId = newConversation.id;
        setActiveConversationId(currentConversationId);
        setConversations(prev => [newConversation, ...prev]);
      }

      // Create user message
      const userMessage: ChatMessage = {
        id: crypto.randomUUID(),
        role: 'user',
        content: messageContent,
        timestamp: Date.now()
      };

      // Update conversation with user message
      const updatedConversation = await conversationService.updateConversation(currentConversationId, userMessage);
      if (!updatedConversation) throw new Error('Failed to update conversation');

      setMessages(updatedConversation.messages);

      // Create a placeholder AI message for streaming
      const aiMessageId = crypto.randomUUID();
      const aiMessage: ChatMessage = {
        id: aiMessageId,
        role: 'assistant',
        content: '',
        timestamp: Date.now()
      };

      // Add the empty AI message to the UI immediately
      setMessages(prev => [...prev, aiMessage]);
      setStreamingMessageId(aiMessageId);
      setStreamingContent('');

      // Create and set streaming controller
      const controller = new StreamingController();
      setStreamingController(controller);

      // Define streaming callback
      const onStreamingChunk: StreamingCallback = (chunk: string) => {
        setStreamingContent(prev => prev + chunk);
      };

      // Process the message with streaming
      const response = await processMessage(updatedConversation, messageContent, undefined, onStreamingChunk, controller);

      // Update the AI message with the final response
      const finalAiMessage: ChatMessage = {
        ...aiMessage,
        content: response
      };

      // Update conversation with final AI message
      const finalConversation = await conversationService.updateConversation(currentConversationId, finalAiMessage);
      if (!finalConversation) throw new Error('Failed to update conversation');

      setMessages(finalConversation.messages);

    } catch (error) {
      console.error('Error processing message:', error);
      // Handle error appropriately
    } finally {
      setIsProcessing(false);
      setInputValue('');
      setStreamingMessageId(null);
      setStreamingContent('');
      setStreamingController(null);
    }
  };

  // Auto-resize textarea
  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const textarea = e.target;
    setInputValue(textarea.value);

    // Reset height to auto to properly calculate new height
    textarea.style.height = 'auto';

    // Set new height based on scrollHeight
    const newHeight = Math.min(Math.max(textarea.scrollHeight, 24), 200);
    textarea.style.height = `${newHeight}px`;
  };

  const toggleLeftPanel = () => setShowLeftPanel(!showLeftPanel);
  const toggleRightPanel = () => setShowRightPanel(!showRightPanel);

  const handleRightPanelResize = (width: number) => {
    setRightPanelWidth(width);
  };

  const handleSendButtonClick = async (event: React.MouseEvent<HTMLButtonElement>) => {
    event.preventDefault();
    if (inputValue.trim()) {
      await handleSendMessage(inputValue);
    }
  };

  const handleKeyPress = async (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      if (inputValue.trim()) {
        await handleSendMessage(inputValue);
      }
    }
  };

  return (
    <>
      {isDbInitializing && (
        <div className={loadingStyles.loadingContainer}>
          <div className={loadingStyles.loadingSpinner}></div>
          <div className={loadingStyles.loadingText}>Initializing Database...</div>
        </div>
      )}
      <ChatTopMenuBar
        showLeftPanel={showLeftPanel}
        showRightPanel={showRightPanel}
        onToggleLeftPanel={toggleLeftPanel}
        onToggleRightPanel={toggleRightPanel}
        selectedModel={selectedModel}
        onModelSelect={handleModelSelect}
      />
      <div
        className={styles['chat-page-container']}
        style={{ paddingTop: 48 }}
      >
        {showLeftPanel && (
          <LeftPanel
            conversations={conversations}
            activeConversationId={activeConversationId}
            onSelectConversation={handleSelectConversation}
            onNewChat={handleNewChat}
            onDeleteConversation={async (id: string) => {
              await conversationService.deleteConversation(id);
              const updatedConversations = await conversationService.loadConversations();
              setConversations(updatedConversations);
              if (activeConversationId === id) {
                if (updatedConversations.length > 0) {
                  handleSelectConversation(updatedConversations[0].id);
                } else {
                  handleNewChat();
                }
              }
            }}
          />
        )}
        <div
          className={styles['chat-main-area']}
          style={{
            marginLeft: showLeftPanel ? '280px' : '0',
            marginRight: showRightPanel ? `${rightPanelWidth}px` : '0',
            width:
              showLeftPanel && showRightPanel
                ? `calc(100% - ${280 + rightPanelWidth}px)`
                : showLeftPanel
                ? 'calc(100% - 280px)'
                : showRightPanel
                ? `calc(100% - ${rightPanelWidth}px)`
                : '100%',
            paddingTop: 48,
          }}
        >
          <div className={styles['messages-container']}>
            {messages.map((message) => {
              // Check for loading messages
              if (message.isLoading) {
                return (
                  <div key={message.id} className={styles['system-message']}>
                    <LoadingAnimation message={message.content.replace(/<div class="loading-animation-container">|<\/div>/g, '')} />
                  </div>
                );
              }

              // Check for system messages
              if (message.id.includes('_system_')) {
                return (
                  <div key={message.id} className={styles['system-message']}>
                    {message.content}
                  </div>
                );
              }

              // Check if this is a streaming message
              if (message.role === 'assistant' && streamingMessageId === message.id) {
                return (
                  <div
                    key={message.id}
                    className={`${styles['message']} ${styles['assistant-message']}`}
                  >
                    <div className={styles['message-content']}>
                      {streamingContent}
                      <span className={styles['streaming-cursor']}>▋</span>
                    </div>
                  </div>
                );
              }

              // Regular user/assistant messages
              return (
                <div
                  key={message.id}
                  className={`${styles['message']} ${message.role === 'user' ? styles['user-message'] : styles['assistant-message']}`}
                >
                  <div className={styles['message-content']}>{message.content}</div>
                </div>
              );
            })}
            {messages.length === 0 && !activeConversationId && (
              <div className={styles['empty-chat-placeholder']}>
                <p className={styles['helper-text']}>Type in the input box below to begin</p>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Memory Context Indicator */}
          {messages.length > 0 && (
            <div className={styles['memory-context-indicator']}>
              <span className={styles['memory-context-text']}>
                Context: {formatMemoryContext(messages.length, memoryLimit)}
              </span>
              <span className={styles['memory-limit-text']}>
                (Memory limit: {memoryLimit} messages)
              </span>
            </div>
          )}

          <div className={styles['input-container']}>
            <textarea
              ref={textareaRef}
              value={inputValue}
              onChange={handleTextareaChange}
              onKeyDown={handleKeyPress}
              placeholder="Type your message..."
              disabled={isProcessing}
              rows={1}
            />
            {streamingMessageId && streamingController && (
              <button
                onClick={handleStopGeneration}
                className={styles['stop-button']}
                title="Stop generation"
              >
                ⏹ Stop
              </button>
            )}
            <button
              onClick={handleSendButtonClick}
              disabled={isProcessing || inputValue.trim() === '' || !selectedModel}
              className={styles['send-button']}
              title={!selectedModel ? 'Please select a model' : 'Send message'}
            >
              {isProcessing ? '...' : '↑'}
            </button>
          </div>
        </div>

        {showRightPanel && (
          <RightPanel
            codeContent={extractedSvg}
            onResize={handleRightPanelResize}
          />
        )}
      </div>
    </>
  );
};

export default ChatPage;