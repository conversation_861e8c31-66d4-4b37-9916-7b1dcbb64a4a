/**
 * Debug script to test message transmission to Ollama
 * This script helps identify where the message corruption is happening
 */

console.log('🔍 Debug Message Transmission Script');
console.log('=====================================');

// Test localStorage handling
function testLocalStorageHandling() {
  console.log('\n📦 Testing localStorage handling...');
  
  // Clear any existing data
  localStorage.removeItem('rohit_initialMessage');
  localStorage.removeItem('rohit_last_processed_timestamp');
  
  // Simulate storing a message from HomePage
  const testMessage = "hello";
  const testModel = {
    id: 'ollama-llama2',
    name: 'llama2',
    provider: 'ollama',
    modelName: 'llama2',
    description: 'Test Ollama model'
  };
  
  const messageData = {
    model: testModel,
    initialMessage: testMessage,
    timestamp: Date.now()
  };
  
  console.log('Storing test message:', messageData);
  localStorage.setItem('rohit_initialMessage', JSON.stringify(messageData));
  
  // Simulate retrieving the message in ChatPage
  const retrievedData = localStorage.getItem('rohit_initialMessage');
  if (retrievedData) {
    const parsed = JSON.parse(retrievedData);
    console.log('Retrieved message:', parsed);
    console.log('Message matches:', parsed.initialMessage === testMessage);
    console.log('Message content:', `"${parsed.initialMessage}"`);
    console.log('Message length:', parsed.initialMessage.length);
  } else {
    console.error('❌ Failed to retrieve message from localStorage');
  }
}

// Test message cleaning function
function testMessageCleaning() {
  console.log('\n🧹 Testing message cleaning...');
  
  const testMessages = [
    "hello",
    "hello <think>some thinking</think>",
    "<think>thinking first</think>hello",
    "sucht 125 <think>First, I need to understand the user's request. They asked for an evaluation of \"draw\" as a verb and provided the number 125. I'll start by looking up information about the verb \"draw.\" It can mean various things depending on the context—like taking something away, causing something to happen, or describing a physical action. ##################################################",
    "normal message without think tags"
  ];
  
  // Simple removeThinkTags implementation for testing
  function removeThinkTags(text) {
    return text.replace(/<think>[\s\S]*?<\/think>/g, '').trim();
  }
  
  testMessages.forEach((msg, index) => {
    const cleaned = removeThinkTags(msg);
    console.log(`Test ${index + 1}:`);
    console.log(`  Original: "${msg.substring(0, 100)}${msg.length > 100 ? '...' : ''}"`);
    console.log(`  Cleaned:  "${cleaned}"`);
    console.log(`  Length change: ${msg.length} -> ${cleaned.length}`);
    console.log('');
  });
}

// Test conversation message handling
function testConversationHandling() {
  console.log('\n💬 Testing conversation message handling...');
  
  // Simulate empty conversation (what should happen for initial messages)
  const emptyConversation = {
    id: 'test',
    messages: [],
    title: 'Test Chat',
    createdAt: Date.now(),
    lastModified: Date.now()
  };
  
  console.log('Empty conversation:', emptyConversation);
  console.log('Message count:', emptyConversation.messages.length);
  
  // Simulate conversation with existing messages (potential source of corruption)
  const conversationWithMessages = {
    id: 'test2',
    messages: [
      {
        id: 'msg1',
        role: 'user',
        content: 'sucht 125 <think>First, I need to understand the user\'s request. They asked for an evaluation of "draw" as a verb and provided the number 125',
        timestamp: Date.now() - 1000
      },
      {
        id: 'msg2',
        role: 'assistant',
        content: 'Some previous response',
        timestamp: Date.now()
      }
    ],
    title: 'Previous Chat',
    createdAt: Date.now() - 2000,
    lastModified: Date.now()
  };
  
  console.log('Conversation with messages:', conversationWithMessages);
  console.log('Message count:', conversationWithMessages.messages.length);
  console.log('Last message content:', conversationWithMessages.messages[conversationWithMessages.messages.length - 1].content);
}

// Test memory limit handling
function testMemoryLimitHandling() {
  console.log('\n🧠 Testing memory limit handling...');
  
  // Simulate getMemoryLimit function
  function getMemoryLimit() {
    const stored = localStorage.getItem('chat_memory_limit');
    if (stored) {
      const parsed = parseInt(stored, 10);
      if (!isNaN(parsed)) {
        return Math.min(Math.max(parsed, 1), 30);
      }
    }
    return 10; // Default
  }
  
  const memoryLimit = getMemoryLimit();
  console.log('Current memory limit:', memoryLimit);
  
  // Test with different conversation sizes
  const testConversations = [
    { messages: [] },
    { messages: new Array(5).fill(null).map((_, i) => ({ id: `msg${i}`, content: `Message ${i}` })) },
    { messages: new Array(15).fill(null).map((_, i) => ({ id: `msg${i}`, content: `Message ${i}` })) },
    { messages: new Array(25).fill(null).map((_, i) => ({ id: `msg${i}`, content: `Message ${i}` })) }
  ];
  
  testConversations.forEach((conv, index) => {
    const recentMessages = conv.messages.slice(-memoryLimit);
    console.log(`Conversation ${index + 1}: ${conv.messages.length} total -> ${recentMessages.length} recent`);
  });
}

// Run all tests
function runAllTests() {
  console.log('🚀 Starting debug tests...\n');
  
  testLocalStorageHandling();
  testMessageCleaning();
  testConversationHandling();
  testMemoryLimitHandling();
  
  console.log('\n✅ Debug tests completed!');
  console.log('\n📋 Summary:');
  console.log('- Check the console output above for any issues');
  console.log('- Pay attention to message content changes');
  console.log('- Verify localStorage data integrity');
  console.log('- Ensure conversation handling is clean');
}

// Export for use in browser console
if (typeof window !== 'undefined') {
  window.debugMessageTransmission = {
    runAllTests,
    testLocalStorageHandling,
    testMessageCleaning,
    testConversationHandling,
    testMemoryLimitHandling
  };
  
  console.log('🔧 Debug functions available on window.debugMessageTransmission');
  console.log('Run window.debugMessageTransmission.runAllTests() to start debugging');
}

// Auto-run if in Node.js environment
if (typeof module !== 'undefined' && module.exports) {
  runAllTests();
}
